package model;

import controller.Constants;
import java.awt.Point;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR> REUVENI
 * Modified for Hare and Hounds game
 */
public class AiModel implements Constants{
    private static int leafs, recursion;
    private static final Object counterLock = new Object();

    public static int getLeafsCount() {
        return leafs;
    }

    public static int getRecursionCount() {
        return recursion;
    }

    private static int pointChecker(Point point){
      if (WHITE_POINTS.contains(point))return 1;
      if (ORANGE_POINTS.contains(point))return 2;
      if (PURPEL_POINTS.contains(point))return 3;
      return -1;
  }

  private static boolean hareWinFormation(List<Point> points, Point harePoint){
      for (Point point : points) {
         if(!Constants.HWIN_FORMATION_HOUNDS.contains(point))return false;
      }
      return harePoint.equals(new Point(1, 3));
}

public static List<Board> getValidBoards(Board fboard, char playerSign){
    List<Move> possibleMoves = fboard.getValidMoves(playerSign);
    List<Board> possibleBoards = new ArrayList<>();
    for (Move move : possibleMoves) {
        Board board = new Board(fboard);
        board.makeMove(move);
        possibleBoards.add(board);
    }
    return possibleBoards;
}

public static void resetCounters(){
    leafs=0;
    recursion=0;
}

   /**
    * Advanced evaluation function for the board state.
    * Returns a score from the perspective of the specified player.
    * Higher positive scores favor the specified player.
    * 
    * @param board The board to evaluate
    * @param depth Current search depth (used for depth-based adjustments)
    * @param playerSign The player whose perspective to evaluate from (HARE_SIGN or HOUND_SIGN)
    * @return Evaluation score from the perspective of playerSign
    */
   public static int eval(Board board, int depth, char playerSign) {
    int score = 0;
    
    // Game ending conditions with depth adjustments for faster wins/slower losses
    if (board.isHareWinner()) {
        return playerSign == HARE_SIGN ? HARE_WIN_SCORE-depth : -HARE_WIN_SCORE+depth;
    }
    if (board.isHoundsWinner()) {
        return playerSign == HARE_SIGN ? -HOUNDS_WIN_SCORE-depth : HOUNDS_WIN_SCORE+depth; 
    }
    if (board.areHoundsStalling()) {
        return playerSign == HARE_SIGN ? HOUNDS_STALL_SCORE-depth : -HOUNDS_STALL_SCORE+depth;
    }
    
    // Evaluate hare blockage
    if (isHareBlocked(board)) {
        score += playerSign == HARE_SIGN ? -400 : 400;
    }
    
    // Mobility evaluation
    score += playerSign == HARE_SIGN ? 
        board.getValidHareMoves().size() * WEIGHT_HARE_MOBILITY : 
        -board.getValidHareMoves().size() * WEIGHT_HARE_MOBILITY;
    
    score += playerSign == HOUND_SIGN ? 
        board.getValidHoundsMoves().size() * WEIGHT_HOUND_MOBILITY : 
        -board.getValidHoundsMoves().size() * WEIGHT_HOUND_MOBILITY;
    
    // Position-based evaluation
    Point harePos = board.getHarePosition();
    List<Point> houndPos = board.getHoundPositions();
    
    // Check for winning formations
    if (hareWinFormation(houndPos, harePos)) {
        score += playerSign == HARE_SIGN ? BONUS_HARE_WIN_FORMATION : -BONUS_HARE_WIN_FORMATION;
    }
    
    // Add detailed evaluations based on player
    if (playerSign == HARE_SIGN) {
        score += evaluateHareMoves(board);
        score += evaluateHarePosition(board);
    } else {
        score += evaluateHoundPositions(board);
    }

    if (playerSign == HOUND_SIGN){
        
    }

    return playerSign == HARE_SIGN ? score + depth*DEPTH_PENALTY : score - depth*DEPTH_PENALTY;
}


private static boolean isHareBlocked(Board board){
    Point harePos = board.getHarePosition();
    List<Point> houndPos = board.getHoundPositions();
    List<Move> hareMoves = board.getValidHareMoves();
    
    // Check if hare has no moves (completely blocked)
    if (hareMoves.isEmpty()) {
        return true; // Hare is blocked completely and can only go backwards
    }

    // Check if all hounds are to the left of the hare
    for (Point hound : houndPos) {
        if (hound.y >= harePos.y) {
            return false;
        }
    }
    
    // Check if hare can reach the left side (column 0)
    for (Move move : hareMoves) {
        if (move.getCol() > harePos.y) {
            return false;
        }
    }    

    return true;
}


    // Helper methods to keep the main evaluation function clean
    private static int evaluateHareMoves(Board board) {
        int score = 0;
        for (Move move : board.getValidHareMoves()) {
            if (board.getPiece(move.getRow(), move.getCol()) == HOUND_SIGN) {
                score -= PENALTY_MOVE_TO_HOUND;
            }
            if (move.getRow() < board.getHarePosition().y) {
                score += REWARD_ADVANCE;
            }
        }
        return score;
    }

    private static int evaluateHoundPositions(Board board) {
        int score = 0;
        List<Point> houndPos = board.getHoundPositions();
        Point harePos = board.getHarePosition();
        
        // Hound positional penalties/rewards
        for (Point hound : houndPos) {
            if (hound.y == board.getHarePosition().y + 1) { // passed the hare line
                score += REWARD_HOUND_PASSED;
            }
            if (hound.y >= board.getHarePosition().y) {
                score += REWARD_HARE_PASS_HOUND;
            }
        }

        // Penalize hounds sharing the same row (once per pair)
        for (int i = 0; i < houndPos.size(); i++) {
            for (int j = i + 1; j < houndPos.size(); j++) {
                if (houndPos.get(i).x == houndPos.get(j).x) {
                    score += REWARD_HOUNDS_SAME_ROW;
                }
            }
        }

        // Evaluate hound spread (covering different rows)
        int[] rowCoverage = new int[BOARD_ROWS];
        for (Point hound : houndPos) {
            rowCoverage[hound.x]++;
        }
        
        int rowsCovered = 0;
        for (int count : rowCoverage) {
            if (count > 0) rowsCovered++;
        }
        
        score += rowsCovered * 75;  // Reward covering more rows
    
        return score;
    }

    // New helper method for hare position evaluation
    private static int evaluateHarePosition(Board board) {
        int score = 0;
        Point harePos = board.getHarePosition();
        List<Point> houndPos = board.getHoundPositions();
        
        // Reward proximity to left edge (goal)
        score += (BOARD_COLS - harePos.y) * WEIGHT_HARE_GOAL_PROXIMITY;
        
        // Reward being in the center rows (more mobility)
        int centerRowBonus = 0;
        if (harePos.x >= 1 && harePos.x <= 3) {
            centerRowBonus = 100;
        }
        score += centerRowBonus;
        
        // Evaluate distance to nearest hound
        int minDistance = Integer.MAX_VALUE;
        for (Point hound : houndPos) {
            int distance = Math.abs(harePos.x - hound.x) + Math.abs(harePos.y - hound.y);
            minDistance = Math.min(minDistance, distance);
        }
        score += minDistance * 50;  // Reward being far from hounds
        
        return score;
    }


   
    /**
    * Minimax algorithm with alpha-beta pruning for game AI.
    *
    * @param depth The current depth of the recursion
    * @param board The board state to evaluate
    * @param alpha The alpha value for alpha-beta pruning
    * @param beta The beta value for alpha-beta pruning
    * @param playerSign The player's sign (HARE_SIGN or HOUND_SIGN)
    * @return The evaluation score of the best move
    */
   public static int minMax(int depth, Board board, int alpha, int beta, boolean isMaximizingPlayer, char player) {
      synchronized(counterLock) {
         recursion++;
      }
      
      // Base case: reached maximum depth or game over
      if (depth == 0 || board.isGameOver()){
         synchronized(counterLock) {
            leafs++;
         }
         return eval(board, depth, player);
      }
      
      List<Board> moves = getValidBoards(board, isMaximizingPlayer ? HARE_SIGN : HOUND_SIGN);
      
      // Early return if no moves available
      if (moves.isEmpty()) {
         synchronized(counterLock) {
            leafs++;
         }
      }
      
      if(isMaximizingPlayer){
            // Maximizing player (Hare)
            int bestScore = Integer.MIN_VALUE;
            
            for (Board nextBoard : moves) {
               int score = minMax(depth - 1, nextBoard, alpha, beta, false, player);
               bestScore = Math.max(score, bestScore);
               alpha = Math.max(alpha, bestScore);
               
               // Alpha-beta pruning
               if (beta <= alpha)
                  break;
            } 
            return bestScore;
       }
       else {
            // Minimizing player (Hounds)
            int bestScore = Integer.MAX_VALUE;
            
            for (Board nextBoard : moves) {
               int score = minMax(depth - 1, nextBoard, alpha, beta, true, player);
               bestScore = Math.min(score, bestScore);
               beta = Math.min(beta, bestScore);
               
               // Alpha-beta pruning
               if (beta <= alpha)
                  break;
            }
            return bestScore;
       }
   }
}
