package model;

import controller.Constants;
import java.awt.Point;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import util.*;

/**
 * <AUTHOR> REUVENI
 * Modified for Hare and Hounds game
 */
@SuppressWarnings("unused")
public class Model implements IModel, Constants
{
   private Board gameBoard;
   private int turnCount;
   private String difficulty = "Easy";
   private String AImsg = "";
   //private int leafs, recursion;
   private final Object counterLock = new Object();

   
   public Model()
   {
      setup();
   }

   // Controller reference not needed

   @Override
   public void setup()
   {
      // Initialize the board using the Board class
      gameBoard = new Board();

      // Reset turn count
      turnCount = 0;
   }

   @Override
   public char[][] getBoard()
   {
      return gameBoard.getGrid();
   }

   public void setMove(Move move)
   {
      // Use the Board class to make the move
      gameBoard.makeMove(move);

      // Increment turn count if it's a hound move
      if (move.getPlayerSign() == HOUND_SIGN) {
         turnCount++;
      }

      if(PRINT_BOARD)
      gameBoard.printBoard();

      Debug.logI(move.toString()+" played successfully !");
   }

   @Override
   public Move getAiMove(char playerSign) {
      try {
   
         // Create a copy of the current game board for AI calculations
         Board aiBoard = new Board(gameBoard);
         List<Board> possibleBoards = getValidBoards(aiBoard, playerSign);

         // Return null if no moves are possible
         if (possibleBoards.isEmpty()) {
            return null;
         }

         // Set up parallel processing using available processors
         int processors = Runtime.getRuntime().availableProcessors();
         ExecutorService executor = Executors.newFixedThreadPool(processors);
         Debug.logI("calculating with: " + processors+" processors at depth of: "+MINMAX_DEPTH);

         List<Future<ScoredBoard>> futureResults = new ArrayList<>();

         boolean isMaximizingPlayer = playerSign == HARE_SIGN;
         
         // Reset counters for performance metrics
         AiModel.resetCounters();

         // Start timing the AI calculation
         Timer timer = new Timer();
         timer.start();

         // Submit each possible board state for evaluation in parallel
         for (Board board : possibleBoards) {
            Future<ScoredBoard> future = executor.submit(() -> {
               int score = AiModel.minMax(MINMAX_DEPTH, board, Integer.MIN_VALUE, Integer.MAX_VALUE, isMaximizingPlayer, playerSign);
               return new ScoredBoard(board, score);
            });
            futureResults.add(future);
         }

         // Store the scored boards 
         List<ScoredBoard> scoredBoards = new ArrayList<>();

         // Collect results from all parallel tasks
         for (Future<ScoredBoard> future : futureResults) {
            try {
               ScoredBoard scoredBoard = future.get();
               scoredBoards.add(scoredBoard);
               Debug.logI(nextMove(scoredBoard) + " score: " + scoredBoard.getScore());
            } catch (Exception e) {
                // Handle exceptions from parallel execution
               Debug.logE("Failed to get scored board: " + e.getMessage());
            }
         }

         timer.stop();
         AImsg = ("Moves: " + String.format("%,d", AiModel.getRecursionCount()) + 
               " evaluated: " + String.format("%,d", AiModel.getLeafsCount()) + 
               " states! in: " + timer.getMillisPassed() + "ms !");

         // Select the best move based on player type
         Board bestBoard = new Board();
         if (playerSign == HARE_SIGN) {
            // Hare wants to maximize score
            bestBoard = Collections.max(scoredBoards, Comparator.comparingInt(ScoredBoard::getScore));
         } else {
            // Hounds want to minimize score
            bestBoard = Collections.min(scoredBoards, Comparator.comparingInt(ScoredBoard::getScore));
         }
         
         // Don't forget to shut down the executor service
         executor.shutdown();
         
         return nextMove(bestBoard);
   } catch (Exception e) {
   }
      return null;
   }

   /**
    * Get all valid moves for the hare
    * @return List of valid positions the hare can move to
    */
   public List<Move> getValidHareMoves() {
      return gameBoard.getValidMoves(gameBoard.getHarePosition(), HARE_SIGN);
   }

   /**
    * Get all valid moves for a specific hound
    * @param houndPos The position of the hound
    * @return List of valid positions the hound can move to
    */
   public List<Move> getValidHoundMoves(Point houndPos) {
      return gameBoard.getValidMoves(houndPos, HOUND_SIGN);
   }

   /**
    * Check if the hare has won (reached the left side of the board)
    * @return true if the hare has won, false otherwise
    */
   public boolean isHareWinner() {
      return gameBoard.isHareWinner();
   }

   /**
    * Check if the hounds have won (trapped the hare)
    * @return true if the hounds have won, false otherwise
    */
   public boolean isHoundsWinner() {
      return gameBoard.isHoundsWinner();
   }

   /**
    * Check if the hounds are stalling (not advancing for 10 turns)
    * @return true if the hounds are stalling, false otherwise
    */
   public boolean areHoundsStalling() {
      return gameBoard.areHoundsStalling();
   }

   /**
    * check if the game is over
    * @return true if the game is over, false otherwise
    */
   public boolean isGameOver() {
      return isHareWinner() || isHoundsWinner() || areHoundsStalling();
   }
   /**
    * Reset the turn count
    */
   public void resetTurnCount() {
      turnCount = 0;
   }

   /**
    * Sets the diffculty when changed
    */

   @Override
   public void setDifficulty(String difficulty) {
      this.difficulty = difficulty;
  }

   /**
    * Check if a move is valid for a specific player
    * @param fromRow The row of the starting position
    * @param fromCol The column of the starting position
    * @param toRow The row of the destination position
    * @param toCol The column of the destination position
    * @param playerSign The player's sign (HARE_SIGN or HOUND_SIGN)
    * @return true if the move is valid, false otherwise
    */
   public boolean isValidMove(Move move) {

      return gameBoard.isValidMove(move);
   }



   public List<Board> getValidBoards(Board fboard, char playerSign){
      List<Move> possibleMoves = new ArrayList<>();
      possibleMoves = fboard.getValidMoves(playerSign);
      List<Board> possibleBoards = new ArrayList<>();
      for (Move move : possibleMoves) {
          Board board = new Board(fboard);
          board.makeMove(move);
          possibleBoards.add(board);
      }
      return possibleBoards;
  }

  /**
   * Determines the move that was made between two board states.
   * Compares board1 (before) with board2 (after) to identify what piece moved and where.
   * @param board1 The initial board state
   * @param board2 The resulting board state after a move
   * @return The Move object representing the change between the two boards
   */
  public Move boardToMove(Board board1, Board board2) {
      char[][] grid1 = board1.getGrid();
      char[][] grid2 = board2.getGrid();

      int fromRow = -1, fromCol = -1;
      int toRow = -1, toCol = -1;
      char sign = ' ';

      // Find where a piece was removed (from position)
      for (int i = 0; i < BOARD_ROWS; i++) {
          for (int j = 0; j < BOARD_COLS; j++) {
              if ((grid1[i][j] == HARE_SIGN || grid1[i][j] == HOUND_SIGN) && 
                  grid1[i][j] != grid2[i][j] && grid2[i][j] == EMPTY_SIGN) {
                  fromRow = i;
                  fromCol = j;
                  sign = grid1[i][j];
              }
          }
      }

      // Find where a piece was added (to position)
      for (int i = 0; i < BOARD_ROWS; i++) {
          for (int j = 0; j < BOARD_COLS; j++) {
              if (grid1[i][j] == EMPTY_SIGN && grid2[i][j] == sign) {
                  toRow = i;
                  toCol = j;
              }
          }
      }

      // Create and return the move
      if (fromRow != -1 && toRow != -1) {
          return new Move(fromRow, fromCol, toRow, toCol, sign);
      }

      return null; // Return null if no move was found
  }

  public Move nextMove(Board board){
      return boardToMove(gameBoard, board);
  }

  public String getAImsg(){
      return AImsg;
  }
   
}

