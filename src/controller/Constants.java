package controller;

import java.awt.Font;
import java.awt.Point;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.swing.ImageIcon;
import model.Move;
import util.Debug;

public interface Constants
{
    // Unicode symbols
    public static final String UNICODE_SETTINGS = "\u2699";  // Gear/Settings symbol
    public static final String UNICODE_CLOSE = "\u274C";     // X symbol
    public static final String UNICODE_SOUND = "\uD83D\uDD0A"; // Speaker symbol
    
    //DEBUG MODE
    //-------------------------------------//
    public static final boolean DEBUG_LOG_MODE = true;
    public static final boolean DEBUG_JUST_ERRORS_WARNINGS = false;
    public static final boolean PRINT_BOARD = false;
    public static final boolean MOVE_LEGALITY_CHECK_OFF = false;
    public static final boolean SIDE_SWITCHER = false;
    public static final boolean SATLLING_CHECK_OFF = false;
    public static final boolean SOUND = true;
    //-------------------------------------//

    // Board dimensions for the Hare and Hounds game
    // We'll use a 3x5 grid to represent the 11 connected points
    public static final int BOARD_ROWS = 4;
    public static final int BOARD_COLS = 5;

    // Minimax depth
    public static final int MINMAX_DEPTH = 10;

    // Player signs
    public static final char HARE_SIGN = 'H';
    public static final char HOUND_SIGN = 'D';
    public static final char EMPTY_SIGN = ' ';

    // UI Constants
    public static final Font FONT_BUTTONS = new Font("Arial", Font.BOLD, 15);
    public static final int BUTTON_SIZE = 50; // Size to fit 50x50 assets with padding
    public static final int ICON_SIZE = 50; // Exact size of the assets
    public static final String WIN_TITLE = "Hare and Hounds Game";

    // Valid positions on the board (11 connected points in an oval layout)
    // We'll use a coordinate system where each point has a unique position
    // The positions are numbered 0-10 for easier reference
    public static final int NUM_POSITIONS = 14;

    // Define position indices for easier reference
    public static final int TOP_ONE = 0;
    public static final int TOP_TWO = 1;
    public static final int TOP_THREE = 2;
    public static final int MIDDLE_ONE = 3;
    public static final int MIDDLE_TWO = 4;
    public static final int MIDDLE_THREE = 5;
    public static final int MIDDLE_FOUR = 6;
    public static final int MIDDLE_FIVE = 7;
    public static final int BOTTOM_ONE = 8;
    public static final int BOTTOM_TWO = 9;
    public static final int BOTTOM_THREE = 10;
    public static final int NEW_ROW_ONE = 11;
    public static final int NEW_ROW_TWO = 12;
    public static final int NEW_ROW_THREE = 13;

    // Map position indices to board coordinates
    public static final Point[] POSITION_COORDINATES = {
        // Top row
        new Point(0, 1), // TOP_ONE        
        new Point(0, 2), // TOP_TWO       
        new Point(0, 3), // TOP_THREE
        // Middle row
        new Point(1, 0), // MIDDLE_ONE
        new Point(1, 1), // MIDDLE_TWO
        new Point(1, 2), // MIDDLE_THREE
        new Point(1, 3), // MIDDLE_FOUR  
        new Point(1, 4), // MIDDLE_FIVE
        // Bottom row
        new Point(2, 1), // BOTTOM_ONE
        new Point(2, 2), // BOTTOM_TWO
        new Point(2, 3),  // BOTTOM_THREE
        // New row
        new Point(3, 1), // NEW_ROW_ONE
        new Point(3, 2), // NEW_ROW_TWO
        new Point(3, 3)  // NEW_ROW_THREE
    };

    // Valid connections between points (adjacency list) - simplified initialization
    public static final Map<Point, List<Point>> VALID_CONNECTIONS = initializeConnections();

    // Helper method to check if a position is valid
    public static boolean isValidPosition(int row, int col) {
        // Check if the position matches any of the defined positions
        for (Point validPos : POSITION_COORDINATES) {
            if (validPos.x == row && validPos.y == col) {
                return true;
            }
        }
        return false;
    }

    // Helper method to check if a move is valid
    public static boolean isValidMove(int fromRow, int fromCol, int toRow, int toCol) {

        Point from = new Point(fromRow, fromCol);
        Point to = new Point(toRow, toCol);

        // Check if there is a connection between the two positions
        List<Point> connections = VALID_CONNECTIONS.get(from);
        if (connections != null) {
            for (Point connection : connections) {
                if (connection.x == to.x && connection.y == to.y) {
                    return true;
                }
            }
        }

        return false;
    }

    public static boolean isValidMove(Move move) {
        return isValidMove(move.getProw(), move.getPcol(), move.getRow(), move.getCol());
    }

    // Initialize the connections map
    private static Map<Point, List<Point>> initializeConnections() {
        Map<Point, List<Point>> connections = new HashMap<>();

        // Connections from TOP_LEFT (0,1)
        connections.put(POSITION_COORDINATES[TOP_ONE], Arrays.asList(
            POSITION_COORDINATES[TOP_TWO],
            POSITION_COORDINATES[MIDDLE_ONE], 
            POSITION_COORDINATES[MIDDLE_TWO],
            POSITION_COORDINATES[MIDDLE_THREE]
        ));

        // Connections from TOP_MIDDLE_LEFT (0,2)
        connections.put(POSITION_COORDINATES[TOP_TWO], Arrays.asList(
            POSITION_COORDINATES[TOP_ONE],
            POSITION_COORDINATES[MIDDLE_THREE],
            POSITION_COORDINATES[TOP_THREE]
        ));

        // Connections from TOP_MIDDLE (0,3)
        connections.put(POSITION_COORDINATES[TOP_THREE], Arrays.asList(
            POSITION_COORDINATES[TOP_TWO], 
            POSITION_COORDINATES[MIDDLE_FIVE],
            POSITION_COORDINATES[MIDDLE_FOUR],
            POSITION_COORDINATES[MIDDLE_THREE]
        ));

        // Connections from TOP_MIDDLE_RIGHT (1,0)
        connections.put(POSITION_COORDINATES[MIDDLE_ONE], Arrays.asList(
            // Removed connection to TOP_MIDDLE
            POSITION_COORDINATES[TOP_ONE],
            POSITION_COORDINATES[MIDDLE_TWO],
            POSITION_COORDINATES[BOTTOM_ONE]
        ));

        // Connections from TOP_RIGHT (1,1)
        connections.put(POSITION_COORDINATES[MIDDLE_TWO], Arrays.asList(
            POSITION_COORDINATES[MIDDLE_ONE], // Add direct connection to TOP_MIDDLE
            POSITION_COORDINATES[TOP_ONE],
            POSITION_COORDINATES[MIDDLE_THREE],
            POSITION_COORDINATES[BOTTOM_ONE]
        ));

        // Connections from MIDDLE_LEFT (1,2)
        connections.put(POSITION_COORDINATES[MIDDLE_THREE], Arrays.asList(
            POSITION_COORDINATES[MIDDLE_TWO],
            POSITION_COORDINATES[TOP_ONE],
            POSITION_COORDINATES[TOP_TWO],
            POSITION_COORDINATES[TOP_THREE],
            POSITION_COORDINATES[MIDDLE_FOUR],
            POSITION_COORDINATES[BOTTOM_THREE],
            POSITION_COORDINATES[BOTTOM_TWO],
            POSITION_COORDINATES[BOTTOM_ONE]

        ));

        // Connections from MIDDLE (1,3)
        connections.put(POSITION_COORDINATES[MIDDLE_FOUR], Arrays.asList(
            POSITION_COORDINATES[TOP_THREE],
            POSITION_COORDINATES[MIDDLE_THREE],
            POSITION_COORDINATES[BOTTOM_THREE],
            POSITION_COORDINATES[MIDDLE_FIVE]
        ));

        // Connections from MIDDLE_RIGHT (1,4)
        connections.put(POSITION_COORDINATES[MIDDLE_FIVE], Arrays.asList(
            POSITION_COORDINATES[TOP_THREE],
            POSITION_COORDINATES[MIDDLE_FOUR],
            POSITION_COORDINATES[BOTTOM_THREE]
        ));

        // Connections from BOTTOM_LEFT (2,1) to include NEW_ROW_ONE
        connections.put(POSITION_COORDINATES[BOTTOM_ONE], Arrays.asList(
            POSITION_COORDINATES[MIDDLE_ONE],
            POSITION_COORDINATES[MIDDLE_TWO],
            POSITION_COORDINATES[MIDDLE_THREE],
            POSITION_COORDINATES[BOTTOM_TWO],
            POSITION_COORDINATES[NEW_ROW_ONE]
        ));

        // Connections from BOTTOM_MIDDLE (2,2) to include NEW_ROW_TWO
        connections.put(POSITION_COORDINATES[BOTTOM_TWO], Arrays.asList(
            POSITION_COORDINATES[BOTTOM_ONE],
            POSITION_COORDINATES[MIDDLE_THREE],
            POSITION_COORDINATES[BOTTOM_THREE],
            POSITION_COORDINATES[NEW_ROW_TWO]
        ));

        // Connections from BOTTOM_RIGHT (2,3) to include NEW_ROW_THREE
        connections.put(POSITION_COORDINATES[BOTTOM_THREE], Arrays.asList(
            POSITION_COORDINATES[BOTTOM_TWO],
            POSITION_COORDINATES[MIDDLE_THREE],
            POSITION_COORDINATES[MIDDLE_FOUR],
            POSITION_COORDINATES[MIDDLE_FIVE],
            POSITION_COORDINATES[NEW_ROW_THREE]
        ));

        // Add connections for the new row positions
        connections.put(POSITION_COORDINATES[NEW_ROW_ONE], Arrays.asList(
            POSITION_COORDINATES[BOTTOM_ONE],
            POSITION_COORDINATES[NEW_ROW_TWO]
        ));

        connections.put(POSITION_COORDINATES[NEW_ROW_TWO], Arrays.asList(
            POSITION_COORDINATES[NEW_ROW_ONE],
            POSITION_COORDINATES[BOTTOM_TWO],
            POSITION_COORDINATES[NEW_ROW_THREE]
        ));

        connections.put(POSITION_COORDINATES[NEW_ROW_THREE], Arrays.asList(
            POSITION_COORDINATES[NEW_ROW_TWO],
            POSITION_COORDINATES[BOTTOM_THREE]
        ));

        return connections;
    }


    static final Set<Point> WHITE_POINTS = Set.of(
        new Point(0, 1),
        new Point(0, 3),
        new Point(2, 1),
        new Point(2, 3)
    );

    static final Set<Point> PURPEL_POINTS = Set.of(
        new Point(0, 2),
        new Point(1, 1),
        new Point(1, 3),
        new Point(2, 2)
    );

    static final Set<Point> ORANGE_POINTS = Set.of(
        new Point(1, 0),
        new Point(1, 2),
        new Point(1, 4)
    );

    //if the hounds are in those places and the hare
    // is between them (in (1, 3)) when the hare turn ends
    // the hare is garenteed a win
    static final Set<Point> HWIN_FORMATION_HOUNDS = Set.of(
        new Point(0, 3),
        new Point(1, 2),
        new Point(2, 3)
    );

    public static final int HARE_WIN_SCORE                = 10000;
    public static final int HOUNDS_WIN_SCORE              = -10000;
    public static final int HOUNDS_STALL_SCORE            = 8000;
    public static final int DEPTH_PENALTY                 = 2;

    public static final int WEIGHT_HARE_MOBILITY          = 150;
    public static final int WEIGHT_HOUND_MOBILITY         = 50;
    public static final int WEIGHT_HARE_GOAL_PROXIMITY    = 200;
    public static final int WEIGHT_HOUND_CLOSURE          = 200;

    public static final int PENALTY_OPPOSITION            = 306;
    public static final int OPPOSITION_DEPTH_PENALTY      = 10;
    public static final int BONUS_HARE_WIN_FORMATION      = 1537;

    public static final int PENALTY_MOVE_TO_HOUND         = 109;
    public static final int REWARD_ADVANCE                = 173;
    public static final int REWARD_HOUND_PASSED           = 500;
    public static final int REWARD_HARE_PASS_HOUND        = 803;
    public static final int REWARD_HOUNDS_SAME_ROW        = 100;


    // Helper method to load sounds
    static javax.sound.sampled.Clip loadSound(String path) {
        try {
            javax.sound.sampled.Clip clip = javax.sound.sampled.AudioSystem.getClip();
            javax.sound.sampled.AudioInputStream inputStream = javax.sound.sampled.AudioSystem.getAudioInputStream(
                Constants.class.getResource(path));
            clip.open(inputStream);
            return clip;
        } catch (Exception e) {
            Debug.logW("Failed to load sound: " + path);
            return null;
        }
    }

    // Load image assets
    public static final ImageIcon ICON_HARE = new ImageIcon(Constants.class.getResource("/assets/hare.png"));
    public static final ImageIcon ICON_HOUND = new ImageIcon(Constants.class.getResource("/assets/hound.png"));
    public static final ImageIcon ICON_APP = new ImageIcon(Constants.class.getResource("/assets/app_icon.png"));

    
    // Load sound assets
    // Example sound assets - update paths as needed
    public static final javax.sound.sampled.Clip SOUND_TAP = loadSound("/assets/Tap_sound.wav");
    public static final javax.sound.sampled.Clip SOUND_START = loadSound("/assets/Start_sound.wav");
    public static final javax.sound.sampled.Clip SOUND_BACKGROUND = loadSound("/assets/background.wav");
    public static final javax.sound.sampled.Clip SOUND_AI = loadSound("/assets/ai.wav");
    public static final javax.sound.sampled.Clip SOUND_LOSER = loadSound("/assets/Loser.wav");
    public static final javax.sound.sampled.Clip SOUND_WINNER = loadSound("/assets/Winner.wav");


}
