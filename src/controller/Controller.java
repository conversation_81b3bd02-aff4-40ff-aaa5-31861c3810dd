package controller;

import model.IModel;
import model.Model;
import model.Move;
import view.IView;

public class Controller implements Constants
{
   private IModel model;
   private IView view;
   private char playerTurn;
   private int prow, pcol; // previous row and column
   private boolean gameOver;

   public Controller(IView view, IModel model)
   {
      this.model = model;
      this.view = view;
      this.gameOver = false;
   }

   public void runGame()
   {
      view.setVisible(true);
      newGame();
   }

   public void newGame()
   {
      playerTurn = HOUND_SIGN; // Hounds go first
      gameOver = false;
      model.setup();
      view.setup(model.getBoard(), playerTurn);
      view.updateInfo("Hounds' Turn - Select a hound and a position to move to");
      view.updateSecondaryInfo("");
   }

   public char getPlayerTurn()
   {
      return playerTurn;
   }

   private void switchTurn()
   {
      if (playerTurn == HARE_SIGN)
         playerTurn = HOUND_SIGN;
      else
         playerTurn = HARE_SIGN;

      // Update the view with the current player's turn
      if (playerTurn == HARE_SIGN) {
         view.updateInfo("Hare's Turn - Select a position to move to");
      } else {
         view.updateInfo("Hounds' Turn - Select a hound and a position to move to");
      }
   }

   public void debugSwitchTurn(){
      if (SIDE_SWITCHER)
      switchTurn();
   }

   /**
    * Handle a click on a board position
    * @param row The row of the clicked position
    * @param col The column of the clicked position
    */
   public void boardButtonClicked(int row, int col)
   {
      view.updateSecondaryInfo("");
      if (gameOver) {
         return; // Ignore clicks if the game is over
      }

      Model gameModel = (Model) model;

      // First click selects the piece, second click selects the destination
      if (model.getBoard()[row][col] == playerTurn) {
         // Player selected their own piece
         prow = row;
         pcol = col;
         view.updateInfo("Selected piece at " + row + "," + col + ". Now select destination.");
      } else if (model.getBoard()[row][col] == EMPTY_SIGN && prow != -1 && pcol != -1) {
         // Player selected an empty cell after selecting a piece
         Move move = new Move(prow, pcol, row, col, playerTurn);
         if (gameModel.isValidMove(move)) {
            makeMove(move);
         } else {
            view.updateInfo("Invalid move! Try again.");
         }
      } else {
         // Invalid selection - either clicked on opponent's piece or empty cell without selecting a piece first
         view.updateInfo("Invalid selection! Select your piece first.");
      }
   }

   public void onDifficultyChanged(String selected){
      model.setDifficulty(selected);

   }

   /**
    * Handle a click on the AI Move button
    */
   public void aiMoveButtonClicked()
   {
      if (gameOver) {
         return; // Ignore clicks if the game is over
      }

      Move aiMove = null;
      try {
         aiMove = model.getAiMove(playerTurn);
      } catch (InterruptedException e) {
         // TODO Auto-generated catch block
         e.printStackTrace();
      }
      if (aiMove != null){
         makeMove(aiMove);
         view.updateSecondaryInfo(model.getAImsg());
      }
      else
         view.updateInfo("No possible move!");
   }

   /**
    * Make a move and check for game over conditions
    * @param move The move to make
    */
   private void makeMove(Move move)
   {
      // Update the model and view
      model.setMove(move);
      view.updateMove(model.getBoard(), move.getRow(), move.getCol(), move.getProw(), move.getPcol());

      // Reset selection
      prow = -1;
      pcol = -1;

      // Check for game over conditions
      Model gameModel = (Model) model;

      if (checkGameOver(gameModel)) {
         return; // Game is over
      }

      // Switch turn if the game is not over
      switchTurn();
   }

   /**
    * Check if the game is over
    * @param gameModel The game model
    * @return true if the game is over, false otherwise
    */
   private boolean checkGameOver(Model gameModel) {
      if (gameModel.isHareWinner()) {
         view.playGameOverSound();
         view.updateInfo("Game Over - Hare Wins!");
         view.showMsg("Game Over - The hare escaped! Hare Wins!", "Game Over");
         gameOver = true;
         return true;
      }

      if (gameModel.isHoundsWinner()) {
         view.playGameOverSound();
         view.updateInfo("Game Over - Hounds Win!");
         view.showMsg("Game Over - Hound trapped the hare! Hounds Wins!", "Game Over");
         gameOver = true;
         return true;
      }

      if (gameModel.areHoundsStalling()) {
         view.playGameOverSound();
         view.updateInfo("Game Over - Hounds are stalling! Hare Wins!");
         view.showMsg("Game Over - the hound stalled for too long! Hare Wins!", "Game Over");
         gameOver = true;
         return true;
      }

      return false;
   }

   
    

}

