package view;

import controller.Controller;

/**
 * Interface for the View component in the MVC architecture.
 * Defines methods for updating the UI and handling user interactions.
 * 
 * <AUTHOR> REUVENI
 * Modified for Hare and Hounds game
 */
public interface IView
{
   /**
    * Sets up the view with the initial board state and player turn
    */
   void setup(char[][] board, char playerTurn);

   /**
    * Updates the view after a move is made
    */
   void updateMove(char[][] board, int row, int col, int prow, int pcol);

   /**
    * Sets the visibility of the view
    */
   void setVisible(boolean status);

   /**
    * Updates the display to show whose turn it is
    */
   void updatePlayerTurn(char playerTurn);

   /**
    * Updates the primary information display
    */
   void updateInfo(String msg);
   
   /**
    * Updates the secondary information display
    */
   void updateSecondaryInfo(String msg);

   /**
    * Shows a message dialog to the user
    */
   void showMsg(String message, String title);

   /**
    * Plays a sound when the game is over
    */
   void playGameOverSound();
   
   /**
    * Sets the controller for this view
    */
   void setController(Controller controller);
}
